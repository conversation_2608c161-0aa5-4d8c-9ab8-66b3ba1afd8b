{"name": "caautobackend", "module": "index.ts", "type": "module", "private": true, "devDependencies": {"@types/bun": "latest", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^2.0.0", "@types/passport": "^1.0.17", "@types/passport-jwt": "^4.0.1"}, "scripts": {"start": "bun run src/index.js", "dev": "bun --watch src/index.ts"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"cors": "^2.8.5", "drizzle-orm": "^0.44.5", "express": "^5.1.0", "express-async-handler": "^1.2.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^2.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "zod": "^4.1.5"}}