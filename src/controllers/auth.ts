import type { Request, Response } from "express";
import Handler from "express-async-handler";

export const login = Handler(async (req: Request, res: Response) => {});

export const register = Handler(async (req: Request, res: Response) => {});

export const logout = Handler(async (req: Request, res: Response) => {});

export const refresh = Handler(async (req: Request, res: Response) => {});

export const verify = Handler(async (req: Request, res: Response) => {});

export const forgotPassword = Handler(
  async (req: Request, res: Response) => {}
);

export const resetPassword = Handler(async (req: Request, res: Response) => {});
