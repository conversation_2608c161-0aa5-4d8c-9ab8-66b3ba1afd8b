import type { Request, Response } from "express";
import Handler from "express-async-handler";

export const getCart = Handler(async (req: Request, res: Response) => {});

export const addToCart = Handler(async (req: Request, res: Response) => {});

export const updateCart = Handler(async (req: Request, res: Response) => {});

export const deleteFromCart = Handler(
  async (req: Request, res: Response) => {}
);

export const clearCart = Handler(async (req: Request, res: Response) => {});
