import type { Request, Response } from "express";
import Handler from "express-async-handler";

export const getAllCategories = Handler(
  async (req: Request, res: Response) => {}
);

export const getCategoryProducts = Handler(
  async (req: Request, res: Response) => {}
);

export const getCategory = Handler(async (req: Request, res: Response) => {});

export const createCategory = Handler(
  async (req: Request, res: Response) => {}
);

export const updateCategory = Handler(
  async (req: Request, res: Response) => {}
);

export const deleteCategory = Handler(
  async (req: Request, res: Response) => {}
);
