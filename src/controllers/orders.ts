import type { Request, Response } from "express";
import Handler from "express-async-handler";

export const getAllOrders = Handler(async (req: Request, res: Response) => {});

export const getOrder = Handler(async (req: Request, res: Response) => {});

export const createOrder = Handler(async (req: Request, res: Response) => {});

export const updateOrder = Handler(async (req: Request, res: Response) => {});

export const confirmOrder = Handler(async (req: Request, res: Response) => {});

export const orderByUserId = Handler(async (req: Request, res: Response) => {});

export const cancelOrder = Handler(async (req: Request, res: Response) => {});

export const deleteOrder = Handler(async (req: Request, res: Response) => {});
