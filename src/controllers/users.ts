import type { Request, Response } from "express";
import Handler from "express-async-handler";

export const profile = Handler(async (req: Request, res: Response) => {});

export const updateProfile = Handler(async (req: Request, res: Response) => {});

export const getAllUsers = Handler(async (req: Request, res: Response) => {});

export const getUser = Handler(async (req: Request, res: Response) => {});

export const updateUser = Handler(async (req: Request, res: Response) => {});

export const deleteUser = Handler(async (req: Request, res: Response) => {});
